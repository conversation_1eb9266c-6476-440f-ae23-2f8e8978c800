@extends('layouts.app')

@section('title', $note->title)

@section('content')
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">
                        <i class="fas fa-sticky-note text-primary me-2"></i>
                        {{ $note->title }}
                    </h4>
                    @if($note->category)
                        <span class="badge mt-2" style="background-color: {{ $note->category->color }}">
                            {{ $note->category->localized_name }}
                        </span>
                    @endif
                </div>
                <div class="btn-group" role="group">
                    <a href="{{ route('notes.edit', $note) }}" class="btn btn-warning btn-sm">
                        <i class="fas fa-edit me-1"></i>
                        {{ __('messages.edit') }}
                    </a>
                    <form action="{{ route('notes.destroy', $note) }}" method="POST" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger btn-sm"
                                onclick="return confirm('{{ __('messages.confirm_delete') }}')">
                            <i class="fas fa-trash me-1"></i>
                            {{ __('messages.delete') }}
                        </button>
                    </form>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-calendar-plus me-1"></i>
                        {{ __('messages.created_at') }}: {{ $note->created_at->format('Y/m/d H:i') }}
                    </small>
                    @if($note->updated_at != $note->created_at)
                        <br>
                        <small class="text-muted">
                            <i class="fas fa-edit me-1"></i>
                            {{ __('messages.last_edit') }}: {{ $note->updated_at->format('Y/m/d H:i') }}
                        </small>
                    @endif
                </div>

                <hr>

                <div class="note-content">
                    {!! nl2br(e($note->content)) !!}
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ route('notes.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    {{ __('messages.back_to_list') }}
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<style>
    .note-content {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #333;
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.375rem;
        border: 1px solid #dee2e6;
    }
</style>
@endsection
