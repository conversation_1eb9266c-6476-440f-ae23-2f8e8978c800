<?php $__env->startSection('title', __('messages.notes_list')); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-sticky-note text-primary me-2"></i>
        <?php echo e(__('messages.notes_list')); ?>

    </h1>
    <a href="<?php echo e(route('notes.create')); ?>" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        <?php echo e(__('messages.new_note')); ?>

    </a>
</div>

<?php if($notes->count() > 0): ?>
    <div class="row">
        <?php $__currentLoopData = $notes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $note): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card note-card h-100">
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0"><?php echo e($note->title); ?></h5>
                            <?php if($note->category): ?>
                                <span class="badge" style="background-color: <?php echo e($note->category->color); ?>">
                                    <?php echo e($note->category->localized_name); ?>

                                </span>
                            <?php endif; ?>
                        </div>
                        <p class="card-text flex-grow-1">
                            <?php echo e(Str::limit($note->content, 100)); ?>

                        </p>
                        <div class="mt-auto">
                            <small class="text-muted d-block mb-2">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo e($note->created_at->diffForHumans()); ?>

                            </small>
                            <div class="btn-group w-100" role="group">
                                <a href="<?php echo e(route('notes.show', $note)); ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i>
                                    <?php echo e(__('messages.view')); ?>

                                </a>
                                <a href="<?php echo e(route('notes.edit', $note)); ?>" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-edit"></i>
                                    <?php echo e(__('messages.edit')); ?>

                                </a>
                                <form action="<?php echo e(route('notes.destroy', $note)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-outline-danger btn-sm"
                                            onclick="return confirm('<?php echo e(__('messages.confirm_delete')); ?>')">
                                        <i class="fas fa-trash"></i>
                                        <?php echo e(__('messages.delete')); ?>

                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
<?php else: ?>
    <div class="text-center py-5">
        <i class="fas fa-sticky-note fa-5x text-muted mb-3"></i>
        <h3 class="text-muted"><?php echo e(__('messages.no_notes')); ?></h3>
        <a href="<?php echo e(route('notes.create')); ?>" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>
            <?php echo e(__('messages.create_note')); ?>

        </a>
    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Note .Laravel\note-app\resources\views/notes/index.blade.php ENDPATH**/ ?>