<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Note extends Model
{
    protected $fillable = [
        'title',
        'content',
        'category_id'
    ];

    /**
     * Get the category that owns the note.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }
}
