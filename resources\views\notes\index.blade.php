@extends('layouts.app')

@section('title', 'لیست یادداشت‌ها')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-sticky-note text-primary me-2"></i>
لیست یاداشت ها
    </h1>
    <a href="{{ route('notes.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        یادداشت جدید
    </a>
</div>

@if($notes->count() > 0)
    <div class="row">
        @foreach($notes as $note)
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card note-card h-100">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ $note->title }}</h5>
                        <p class="card-text flex-grow-1">
                            {{ Str::limit($note->content, 100) }}
                        </p>
                        <div class="mt-auto">
                            <small class="text-muted d-block mb-2">
                                <i class="fas fa-clock me-1"></i>
                                {{ $note->created_at->diffForHumans() }}
                            </small>
                            <div class="btn-group w-100" role="group">
                                <a href="{{ route('notes.show', $note) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i>
                                    مشاهده
                                </a>
                                <a href="{{ route('notes.edit', $note) }}" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-edit"></i>
                                    ویرایش
                                </a>
                                <form action="{{ route('notes.destroy', $note) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger btn-sm" 
                                            onclick="return confirm('آیا مطمئن هستید که می‌خواهید این یادداشت را حذف کنید؟')">
                                        <i class="fas fa-trash"></i>
                                        حذف
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
@else
    <div class="text-center py-5">
        <i class="fas fa-sticky-note fa-5x text-muted mb-3"></i>
        <h3 class="text-muted">هیچ یادداشتی وجود ندارد</h3>
        <p class="text-muted mb-4">برای شروع، اولین یادداشت خود را ایجاد کنید.</p>
        <a href="{{ route('notes.create') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>
            ایجاد یادداشت جدید
        </a>
    </div>
@endif
@endsection
