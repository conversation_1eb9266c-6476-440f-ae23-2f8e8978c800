@extends('layouts.app')

@section('title', __('messages.notes_list'))

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-sticky-note text-primary me-2"></i>
        {{ __('messages.notes_list') }}
    </h1>
    <a href="{{ route('notes.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        {{ __('messages.new_note') }}
    </a>
</div>

@if($notes->count() > 0)
    <div class="row">
        @foreach($notes as $note)
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card note-card h-100">
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">{{ $note->title }}</h5>
                            @if($note->category)
                                <span class="badge" style="background-color: {{ $note->category->color }}">
                                    {{ $note->category->localized_name }}
                                </span>
                            @endif
                        </div>
                        <p class="card-text flex-grow-1">
                            {{ Str::limit($note->content, 100) }}
                        </p>
                        <div class="mt-auto">
                            <small class="text-muted d-block mb-2">
                                <i class="fas fa-clock me-1"></i>
                                {{ $note->created_at->diffForHumans() }}
                            </small>
                            <div class="btn-group w-100" role="group">
                                <a href="{{ route('notes.show', $note) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i>
                                    {{ __('messages.view') }}
                                </a>
                                <a href="{{ route('notes.edit', $note) }}" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-edit"></i>
                                    {{ __('messages.edit') }}
                                </a>
                                <form action="{{ route('notes.destroy', $note) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger btn-sm"
                                            onclick="return confirm('{{ __('messages.confirm_delete') }}')">
                                        <i class="fas fa-trash"></i>
                                        {{ __('messages.delete') }}
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
@else
    <div class="text-center py-5">
        <i class="fas fa-sticky-note fa-5x text-muted mb-3"></i>
        <h3 class="text-muted">{{ __('messages.no_notes') }}</h3>
        <a href="{{ route('notes.create') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>
            {{ __('messages.create_note') }}
        </a>
    </div>
@endif
@endsection
