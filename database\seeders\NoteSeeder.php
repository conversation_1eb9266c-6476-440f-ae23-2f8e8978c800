<?php

namespace Database\Seeders;

use App\Models\Note;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NoteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $notes = [
            [
                'title' => 'خوش آمدید به سیستم یادداشت‌ها',
                'content' => 'این اولین یادداشت شما است. شما می‌توانید یادداشت‌های خود را ایجاد، ویرایش و حذف کنید. این سیستم با Laravel ساخته شده است و تمام عملیات CRUD را پشتیبانی می‌کند.'
            ],
            [
                'title' => 'ویژگی‌های سیستم',
                'content' => 'این سیستم دارای ویژگی‌های زیر است:
- ایجاد یادداشت جدید
- مشاهده لیست یادداشت‌ها
- ویرا<PERSON>ش یادداشت‌های موجود
- حذف یادداشت‌ها
- رابط کاربری زیبا و کاربرپسند
- پشتیبانی از زبان فارسی'
            ],
            [
                'title' => 'نکات مهم',
                'content' => 'لطفاً موارد زیر را در نظر بگیرید:
- عنوان یادداشت الزامی است
- محتوای یادداشت نمی‌تواند خالی باشد
- تاریخ ایجاد و ویرایش به صورت خودکار ثبت می‌شود
- امکان جستجو و فیلتر کردن یادداشت‌ها در نسخه‌های آینده اضافه خواهد شد'
            ]
        ];

        foreach ($notes as $note) {
            Note::create($note);
        }
    }
}
