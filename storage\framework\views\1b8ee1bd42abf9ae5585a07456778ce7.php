<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $__env->yieldContent('title', 'مدیریت یادداشت‌ها'); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Tahoma', sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        .btn {
            border-radius: 0.375rem;
        }
        .note-card {
            transition: transform 0.2s;
        }
        .note-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="<?php echo e(route('notes.index')); ?>">
                <i class="fas fa-sticky-note me-2"></i>
                <?php echo e(__('messages.note_management')); ?>

            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<?php echo e(route('notes.index')); ?>">
                    <i class="fas fa-list me-1"></i>
                    <?php echo e(__('messages.notes_list')); ?>

                </a>
                <a class="nav-link" href="<?php echo e(route('notes.create')); ?>">
                    <i class="fas fa-plus me-1"></i>
                    <?php echo e(__('messages.new_note')); ?>

                </a>
                <a class="nav-link" href="<?php echo e(route('categories.index')); ?>">
                    <i class="fas fa-tags me-1"></i>
                    <?php echo e(__('messages.categories')); ?>

                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        <?php echo e(__('messages.language')); ?>

                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo e(route('language.switch', 'fa')); ?>"><?php echo e(__('messages.persian')); ?></a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('language.switch', 'en')); ?>"><?php echo e(__('messages.english')); ?></a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo e(session('success')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo e(session('error')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\Note .Laravel\note-app\resources\views/layouts/app.blade.php ENDPATH**/ ?>