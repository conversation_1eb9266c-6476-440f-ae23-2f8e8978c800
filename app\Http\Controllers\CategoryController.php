<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;

class Category<PERSON><PERSON>roller extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categories = Category::withCount('notes')->latest()->get();
        return view('categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
            'name_en' => 'nullable|max:255',
            'description' => 'nullable',
            'description_en' => 'nullable',
            'color' => 'required|regex:/^#[0-9A-Fa-f]{6}$/'
        ]);

        Category::create($request->all());

        return redirect()->route('categories.index')
            ->with('success', __('messages.category_created'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Category $category)
    {
        $notes = $category->notes()->latest()->get();
        return view('categories.show', compact('category', 'notes'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Category $category)
    {
        return view('categories.edit', compact('category'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Category $category)
    {
        $request->validate([
            'name' => 'required|max:255',
            'name_en' => 'nullable|max:255',
            'description' => 'nullable',
            'description_en' => 'nullable',
            'color' => 'required|regex:/^#[0-9A-Fa-f]{6}$/'
        ]);

        $category->update($request->all());

        return redirect()->route('categories.index')
            ->with('success', __('messages.category_updated'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Category $category)
    {
        $category->delete();

        return redirect()->route('categories.index')
            ->with('success', __('messages.category_deleted'));
    }
}
