@extends('layouts.app')

@section('title', __('messages.edit_note'))

@section('content')
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-edit text-warning me-2"></i>
                    {{ __('messages.edit_note') }}
                </h4>
            </div>
            <div class="card-body">
                <form action="{{ route('notes.update', $note) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="mb-3">
                        <label for="title" class="form-label">{{ __('messages.note_title') }}</label>
                        <input type="text"
                               class="form-control @error('title') is-invalid @enderror"
                               id="title"
                               name="title"
                               value="{{ old('title', $note->title) }}"
                               placeholder="{{ __('messages.enter_note_title') }}"
                               required>
                        @error('title')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="category_id" class="form-label">{{ __('messages.category') }}</label>
                        <select class="form-control @error('category_id') is-invalid @enderror"
                                id="category_id"
                                name="category_id">
                            <option value="">{{ __('messages.uncategorized') }}</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ old('category_id', $note->category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->localized_name }}
                                </option>
                            @endforeach
                        </select>
                        @error('category_id')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">{{ __('messages.note_content') }}</label>
                        <textarea class="form-control @error('content') is-invalid @enderror"
                                  id="content"
                                  name="content"
                                  rows="8"
                                  placeholder="{{ __('messages.enter_note_content') }}"
                                  required>{{ old('content', $note->content) }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="{{ route('notes.show', $note) }}" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-1"></i>
                                {{ __('messages.back') }}
                            </a>
                            <a href="{{ route('notes.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-list me-1"></i>
                                {{ __('messages.notes_list') }}
                            </a>
                        </div>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-1"></i>
                            {{ __('messages.update_note') }}
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    {{ __('messages.last_edit') }}: {{ $note->updated_at->format('Y/m/d H:i') }}
                </small>
            </div>
        </div>
    </div>
</div>
@endsection
