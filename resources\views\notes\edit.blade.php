@extends('layouts.app')

@section('title', 'ویرایش یادداشت')

@section('content')
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-edit text-warning me-2"></i>
                    ویرایش یادداشت
                </h4>
            </div>
            <div class="card-body">
                <form action="{{ route('notes.update', $note) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان یادداشت</label>
                        <input type="text" 
                               class="form-control @error('title') is-invalid @enderror" 
                               id="title" 
                               name="title" 
                               value="{{ old('title', $note->title) }}" 
                               placeholder="عنوان یادداشت را وارد کنید..."
                               required>
                        @error('title')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">محتوای یادداشت</label>
                        <textarea class="form-control @error('content') is-invalid @enderror" 
                                  id="content" 
                                  name="content" 
                                  rows="8" 
                                  placeholder="محتوای یادداشت را وارد کنید..."
                                  required>{{ old('content', $note->content) }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="{{ route('notes.show', $note) }}" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-1"></i>
                                بازگشت
                            </a>
                            <a href="{{ route('notes.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-list me-1"></i>
                                لیست یادداشت‌ها
                            </a>
                        </div>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-1"></i>
                            به‌روزرسانی یادداشت
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    آخرین ویرایش: {{ $note->updated_at->format('Y/m/d H:i') }}
                </small>
            </div>
        </div>
    </div>
</div>
@endsection
